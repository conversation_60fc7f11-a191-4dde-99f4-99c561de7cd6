---
title: Overview
slug: /
---

# Welcome to CocoIndex

Prepare high quality data that is tailored for the purpose is essential for a successful AI application in production.   

CocoIndex is a data indexing platform for AI use cases - semantic search, RAG, agentic workflow on top of embedding / knowledge graph etc.  CocoIndex aims to be the best in class scalable data indexing infrastructure with built in observability and lineage. 

CocoIndex can help you connecting to all the data sources, identify the best indexing strategy and setup the most robust pipeline -  chunking, embedding model, deduping/reconciling, vector stores, knowledge graph etc.  And then providing standard API to access the index.

CocoIndex does all the heavy lifting work and plumbing for the data, so you can focus on your business logic and build your AI application on top of robust data indices.

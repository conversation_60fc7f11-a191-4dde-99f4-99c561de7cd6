---
title: CLI
description: CocoIndex CLI
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# CocoIndex CLI

CocoIndex CLI is a standalone tool for easily managing and inspecting your flows and indexes.

## Invoke the CLI

Once CocoIndex is installed, you can invoke the CLI directly using the `cocoindex` command. Most commands require an `APP_TARGET` argument, which tells the CLI where your flow definitions are located.

### APP_TARGET Format

The `APP_TARGET` can be:
1.  A **path to a Python file** defining your flows (e.g., `main.py`, `path/to/my_flows.py`).
2.  An **installed Python module name** that contains your flow definitions (e.g., `my_package.flows`).
3.  For commands that operate on a *specific flow* (like `show`, `update`, `evaluate`), you can combine the application reference with a flow name:
    * `path/to/my_flows.py:MyFlow`
    * `my_package.flows:MyFlow`

### Environment Variables

Environment variables are needed as CocoIndex library settings, as described in [CocoIndex Settings](settings#list-of-environment-variables).

You can set environment variables in an environment file.

*   By default, the `cocoindex` CLI searches upward from the current directory for a `.env` file.
*   You can use `--env-file <path>` to specify one explicitly:

    ```sh
    cocoindex --env-file path/to/custom.env <COMMAND> ...
    ```

Loaded variables do *NOT* override existing system ones.
If no file is found, only existing system environment variables are used.

### Global Options

CocoIndex CLI supports the following global options:

* `--env-file <path>`: Load environment variables from a specified `.env` file. If not provided, `.env` in the current directory is loaded if it exists.
* `--version`: Show the CocoIndex version and exit.
* `--help`: Show the main help message and exit.

:::caution Deprecated Usage

The old method of invoking the CLI using `python main.py cocoindex ...` via the `@cocoindex.main_fn()` decorator is now deprecated. Please remove `@cocoindex.main_fn()` from your scripts and use the standalone cocoindex command as described.

:::

## Subcommands

The following subcommands are available:

| Subcommand | Description |
| ---------- | ----------- |
| `ls` | List all flows present in the given file/module. Or list all persisted flows under the current app namespace if no file/module specified. |
| `show` | Show the spec and schema for a specific flow. |
| `setup` | Check and apply backend setup changes for flows, including the internal and target storage (to export). |
| `drop` | Drop the backend setup for specified flows. |
| `update` | Update the index defined by the flow. |
| `evaluate` | Evaluate the flow and dump flow outputs to files.  Instead of updating the index, it dumps what should be indexed to files. Mainly used for evaluation purpose. |
| `server` | Start a HTTP server providing REST APIs. It will allow tools like CocoInsight to access the server. |

Use `--help` to see the full list of subcommands, and `subcommand --help` to see the usage of a specific one.

```sh
cocoindex --help       # Show all subcommands
cocoindex show --help  # Show usage of "show" subcommand
```
#!/usr/bin/env python3
"""
Standalone Enterprise KG Main Script

This script can be run independently to process enterprise documents
and build a knowledge graph in Neo4j.

Usage:
    python main.py
    python main.py --documents /path/to/docs --neo4j-uri bolt://localhost:7687
"""

import os
import sys
import argparse
import logging
from typing import List
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add current directory to path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from standalone_processor import create_standalone_processor, StandaloneDocumentProcessor
from utils.helpers import setup_environment, print_setup_summary, create_sample_documents
from storage.neo4j_client import Neo4jClient, Neo4jConnection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Enterprise Knowledge Graph Processor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Process documents in current directory with default settings
    python main.py

    # Process specific directory with custom Neo4j connection
    python main.py --documents /path/to/docs --neo4j-uri bolt://your-neo4j.com:7687

    # Use different LLM provider
    python main.py --llm-provider anthropic --llm-model claude-3-5-sonnet-latest

    # Create sample documents and process them
    python main.py --create-samples --documents sample_docs
        """
    )

    # Document processing options
    parser.add_argument(
        "--documents", "-d",
        default=os.getenv("DOCUMENTS_PATH", "documents"),
        help="Path to documents directory (default: documents)"
    )

    parser.add_argument(
        "--file-patterns",
        nargs="+",
        default=[".md", ".txt"],
        help="File patterns to process (default: .md .txt)"
    )

    parser.add_argument(
        "--create-samples",
        action="store_true",
        help="Create sample documents for testing"
    )

    # Neo4j connection options
    parser.add_argument(
        "--neo4j-uri",
        default=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        help="Neo4j URI (default: bolt://localhost:7687)"
    )

    parser.add_argument(
        "--neo4j-user",
        default=os.getenv("NEO4J_USER", "neo4j"),
        help="Neo4j username (default: neo4j)"
    )

    parser.add_argument(
        "--neo4j-password",
        default=os.getenv("NEO4J_PASSWORD", "password"),
        help="Neo4j password (default: password)"
    )

    parser.add_argument(
        "--neo4j-database",
        default=os.getenv("NEO4J_DATABASE"),
        help="Neo4j database name (optional)"
    )

    # LLM options
    parser.add_argument(
        "--llm-provider",
        choices=["openai", "anthropic", "gemini", "openrouter"],
        default=os.getenv("LLM_PROVIDER", "openai"),
        help="LLM provider (default: openai)"
    )

    parser.add_argument(
        "--llm-model",
        default=os.getenv("LLM_MODEL", "gpt-4o"),
        help="LLM model name (default: gpt-4o)"
    )

    parser.add_argument(
        "--llm-api-key",
        help="LLM API key (will use environment variable if not provided)"
    )

    # Processing options
    parser.add_argument(
        "--skip-summarization",
        action="store_true",
        help="Skip document summarization step"
    )

    parser.add_argument(
        "--focus-relationships",
        nargs="+",
        default=["involved_in", "mentions", "works_for", "manages", "reports_to"],
        help="Relationship types to focus on"
    )

    # Output options
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Process documents but don't store in Neo4j"
    )

    return parser.parse_args()


def test_neo4j_connection(neo4j_client: Neo4jClient) -> bool:
    """Test Neo4j connection."""
    try:
        # Try a simple query
        driver = neo4j_client._get_driver()
        with driver.session(database=neo4j_client.connection.database) as session:
            result = session.run("RETURN 1 as test")
            test_value = result.single()["test"]
            if test_value == 1:
                logger.info("✓ Neo4j connection successful")
                return True
    except Exception as e:
        logger.error(f"✗ Neo4j connection failed: {e}")
        return False

    return False


def print_processing_summary(results: List):
    """Print a summary of processing results."""
    total_docs = len(results)
    successful_docs = sum(1 for r in results if r.is_completed)
    failed_docs = total_docs - successful_docs

    total_time = sum(r.processing_duration_seconds or 0 for r in results)
    avg_time = total_time / total_docs if total_docs > 0 else 0

    print("\n" + "="*50)
    print("PROCESSING SUMMARY")
    print("="*50)
    print(f"Total documents processed: {total_docs}")
    print(f"Successfully processed: {successful_docs}")
    print(f"Failed: {failed_docs}")
    print(f"Total processing time: {total_time:.2f} seconds")
    print(f"Average time per document: {avg_time:.2f} seconds")

    if failed_docs > 0:
        print("\nFailed documents:")
        for result in results:
            if not result.is_completed and result.errors:
                print(f"  - {result.document_id}: {result.errors[0]}")

    print("="*50)


def main():
    """Main function."""
    args = parse_arguments()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    print("🚀 Enterprise Knowledge Graph Processor")
    print("="*50)

    # Step 1: Environment setup
    if args.create_samples:
        print("📁 Creating sample documents...")
        try:
            created_files = create_sample_documents(args.documents)
            print(f"✓ Created {len(created_files)} sample documents in {args.documents}/")
        except Exception as e:
            print(f"✗ Failed to create sample documents: {e}")
            return 1

    # Check if documents directory exists
    if not os.path.exists(args.documents):
        print(f"✗ Documents directory not found: {args.documents}")
        print("💡 Use --create-samples to create sample documents")
        return 1

    # Step 2: Test Neo4j connection
    print(f"\n🔗 Testing Neo4j connection to {args.neo4j_uri}...")
    neo4j_conn = Neo4jConnection(
        uri=args.neo4j_uri,
        user=args.neo4j_user,
        password=args.neo4j_password,
        database=args.neo4j_database
    )
    neo4j_client = Neo4jClient(neo4j_conn)

    if not test_neo4j_connection(neo4j_client):
        print("💡 Make sure Neo4j is running and credentials are correct")
        if not args.dry_run:
            return 1

    # Step 3: Create processor
    print(f"\n🤖 Initializing LLM client ({args.llm_provider}: {args.llm_model})...")
    try:
        processor = create_standalone_processor(
            neo4j_uri=args.neo4j_uri,
            neo4j_user=args.neo4j_user,
            neo4j_password=args.neo4j_password,
            llm_provider=args.llm_provider,
            llm_model=args.llm_model,
            llm_api_key=args.llm_api_key
        )

        # Update processor settings
        processor.enable_summarization = not args.skip_summarization
        processor.focus_relationships = args.focus_relationships

        print("✓ Processor initialized successfully")

    except Exception as e:
        print(f"✗ Failed to initialize processor: {e}")
        return 1

    # Step 4: Process documents
    print(f"\n📄 Processing documents from {args.documents}...")
    print(f"   File patterns: {args.file_patterns}")
    print(f"   Focus relationships: {args.focus_relationships}")

    if args.dry_run:
        print("   🔍 DRY RUN MODE - No data will be stored")

    try:
        start_time = datetime.now()

        # Override the storage method for dry run
        if args.dry_run:
            original_store = processor._store_in_neo4j
            processor._store_in_neo4j = lambda rels, doc: logger.info(f"DRY RUN: Would store {len(rels)} relationships")

        results = processor.process_directory(args.documents, args.file_patterns)

        end_time = datetime.now()

        # Print results
        print_processing_summary(results)

        # Print some sample relationships if successful
        if results and any(r.relationship_extraction_completed for r in results):
            print("\n📊 Sample extracted relationships:")
            print("   (Check Neo4j for complete data)")
            print("   Example queries:")
            print("   MATCH (n)-[r]->(m) RETURN n.name, type(r), m.name LIMIT 10")
            print("   MATCH (p:Entity)-[:INVOLVED_IN]->(proj:Entity) RETURN p.name, proj.name")

    except KeyboardInterrupt:
        print("\n⚠️  Processing interrupted by user")
        return 1
    except Exception as e:
        print(f"\n✗ Processing failed: {e}")
        logger.exception("Detailed error:")
        return 1
    finally:
        # Clean up
        neo4j_client.close()

    print("\n✅ Processing completed successfully!")
    return 0


if __name__ == "__main__":
    sys.exit(main())

/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* Import Questrial font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Questrial&display=swap');

/* You can override the default Infima variables here. */
:root {
  /* Iris color scheme from Radix UI */
  --ifm-color-primary: #5B5BD6;
  --ifm-color-primary-dark: #4D4DCC;
  --ifm-color-primary-darker: #4040C8;
  --ifm-color-primary-darkest: #3333B3;
  --ifm-color-primary-light: #7373DE;
  --ifm-color-primary-lighter: #8F8FE6;
  --ifm-color-primary-lightest: #ABABEF;

  
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
  --my-color-text-black: #111827;
  
  /* Additional colors */
  --ifm-navbar-background-color: var(--ifm-background-color);
  --ifm-background-color: #ffffff;
  --ifm-footer-background-color: #ffffff;
  --ifm-menu-color: #374151;
  --ifm-toc-link-color: #374151;
  
  /* Theme colors for breadcrumbs */
  --theme-color-text-light: #6b7280;
  --theme-color-text-default: #111827;
  --theme-color-keyline: #e5e7eb;
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #8F8FE6;
  --ifm-color-primary-dark: #7373DE;
  --ifm-color-primary-darker: #5B5BD6;
  --ifm-color-primary-darkest: #4D4DCC;
  --ifm-color-primary-light: #ABABEF;
  --ifm-color-primary-lighter: #C4C4F5;
  --ifm-color-primary-lightest: #E1E1FF;
  
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
  --my-color-text-black: #f9fafb;
  
  /* Dark mode specific colors */
  --ifm-navbar-background-color: var(--ifm-background-color);
  --ifm-background-color: #111827;
  --ifm-footer-background-color: #111827;
  --ifm-menu-color: #f3f4f6;
  --ifm-toc-link-color: #f3f4f6;
  
  /* Dark mode theme colors for breadcrumbs */
  --theme-color-text-light: #9ca3af;
  --theme-color-text-default: #f9fafb;
  --theme-color-keyline: #374151;
}

.markdown {
  line-height: 150%;

  code {
    font-size: var(--ifm-code-font-size);
  }

  a {
    font-weight: var(--ifm-font-weight-semibold);
  }

  h1,
  h1:first-child,
  h2,
  h3,
  h4,
  h5,
  h6 {
    --ifm-h1-font-size: 1.8rem;
    --ifm-h1-vertical-rhythm-bottom: 0.5;
    --ifm-h2-font-size: 1.5rem;
    --ifm-heading-vertical-rhythm-bottom: 1;
    --ifm-h3-font-size: 1.2rem;
    --ifm-h4-font-size: 1rem;
    --ifm-h5-font-size: 0.8rem;
  }
}

.navbar__title {
  font-family: 'Questrial', sans-serif;
  font-size: 1.125rem; /* 18px */
  color: var(--my-color-text-black);
}

.navbar {
  box-shadow: none;
  font-size: 0.875rem; /* 14px */
  border-bottom: 1px solid var(--ifm-color-emphasis-200);
}

.sidebarItemTitle_pO2u,
.navbar__items {
  font-family: 'Questrial', sans-serif;
}

.footer {
  padding: 4rem 2rem;
  border-top: 1px solid var(--ifm-color-emphasis-100);
}

.footer__title {
  font-family: 'Questrial', sans-serif;
  font-size: 1rem; 
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--my-color-text-black);
}

.footer__item {
  font-family: 'Questrial', sans-serif;
  font-size: 0.9rem;
  padding: 0.25rem 0;
}

.footer__copyright {
  font-family: 'Questrial', sans-serif;
  font-size: 0.9rem;
  margin-top: 3rem;
  text-align: left;
  color: var(--ifm-color-emphasis-600);
}

.footer__link-item {
  color: var(--ifm-color-emphasis-700);
  line-height: 2;
}

.footer__link-item:hover {
  color: var(--ifm-color-primary);
  text-decoration: none;
}

.theme-doc-sidebar-menu {
  font-size: 0.875rem; /* 14px */
}

.table-of-contents {
  font-size: 0.8125rem; /* 13px */
}

.breadcrumbs {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  color: var(--theme-color-text-light);

  .breadcrumbs__item:first-child {
    display: none;
  }
  .breadcrumbs__link {
    padding: 0;
    background: none;
  }
}

/* Sidebar caret styles */
.menu__caret:before,
.menu__link--sublist-caret:after {
  background: var(--ifm-menu-link-sublist-icon) 50% / 1rem 1rem;
}

.navbar__item.navbar-github-link{
  display: block;
  width: 120px;
}
